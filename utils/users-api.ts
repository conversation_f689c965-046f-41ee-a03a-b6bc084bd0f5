/**
 * Authentication API utilities for making requests to the auth endpoints
 */

import { encryptPassword } from "@/utils/encryption"
import { api } from "@/utils/api"

// Types for registration data
export interface RegisterData {
  username: string
  password: string
  name: string
  email: string
  emailCode: string
  gender: string
  organization: string
  identity: string
  region: string
  phone: string
  captcha: string
}

const ApiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:3001"

// Types for verification code request
export interface VerificationCodeRequest {
  email: string
}

// Types for forgot password request
export interface ForgotPasswordRequest {
  email: string
  turnstileToken: string
}

// Types for reset password request
export interface ResetPasswordRequest {
  token: string
  password: string
  turnstileToken: string
}

// Types for API response
export interface ApiResponse<T> {
  code: number
  data: T
  msg: string
}

// Types for user profile data from API
export interface UserProfileData {
  id: number
  username: string
  photo_link: string
  name: string
  role: string
  membership: string // 原 capacity 字段更名为 membership
  gender: string
  country: string
  organization: string
  email_id: number
  email: string
  email_verified: boolean
  phone: string
  position: string
  bio: string
  interest: string
  expertise: string[] // 统一使用 expertise，删除 expertises
  create_time: string
}

// Types for user profile update request
export interface UpdateUserProfileRequest {
  name?: string
  email?: string
  gender?: string
  organization?: string
  identity?: string // 用户身份/职业类型，如：学生、教授、研究员等
  region?: string
  phone?: string
  bio?: string
  expertise?: string[] // 统一使用 expertise
  country?: string
  position?: string
  interest?: string
}

// Types for email update request
export interface UpdateEmailRequest {
  email: string
}

// Types for username check response
export interface UsernameCheckResponse {
  exist: boolean
}

/**
 * Send a registration request to the local API
 * @param data Registration data
 * @param turnstileToken
 * @returns Promise with the response data
 */
export async function sendRegistrationRequest(data: RegisterData, turnstileToken: string): Promise<any> {
  try {
    const response = await fetch(`${ApiBaseUrl}/api/auth/register`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        data,
        secret: process.env.TURNSTILE_SECRET_KEY,
        response: turnstileToken,
      }),
    })

    const responseData = (await response.json()) as {
      code?: number
      data?: any
      msg?: string
      message?: string
      [key: string]: any
    }

    // 检查响应是否成功 - 支持新旧格式
    if (!response.ok || (responseData.code && responseData.code !== 200)) {
      throw new Error(responseData.msg || responseData.message || "Registration failed")
    }

    return responseData
  } catch (error) {
    console.error("Registration request error:", error)
    throw error
  }
}

/**
 * Request an email verification code
 * @param email Email address to send the verification code to
 * @returns Promise with the response data
 */
export async function requestVerificationCode(email: string): Promise<any> {
  try {
    const response = await fetch(`${ApiBaseUrl}/api/auth/send-verification-code`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ email }),
    })

    const responseData = (await response.json()) as {
      code?: number
      data?: any
      msg?: string
      message?: string
      [key: string]: any
    }

    // 检查响应是否成功 - 支持新旧格式
    if (!response.ok || (responseData.code && responseData.code !== 200)) {
      throw new Error(responseData.msg || responseData.message || "Failed to send verification code")
    }

    return responseData
  } catch (error) {
    console.error("Verification code request error:", error)
    throw error
  }
}

/**
 * Send a forgot password request
 * @param email Email address to send the reset link to
 * @param turnstileToken Cloudflare Turnstile verification token
 * @returns Promise with the response data
 */
export async function sendForgotPasswordRequest(email: string, turnstileToken: string): Promise<any> {
  try {
    // 使用API工具类发送请求
    const result = await api.post<{
      code?: number
      data?: any
      msg?: string
      message?: string
    }>("/api/auth/forgot-password", {
      email,
      "cf-turnstile-response": turnstileToken,
    })

    // 检查是否有标准API响应格式
    if (result && typeof result === "object" && "code" in result) {
      if (result.code === 200) {
        return result
      } else {
        throw new Error(result.msg || result.message || "Failed to send password reset link")
      }
    } else {
      // 处理直接返回成功的情况（向后兼容）
      return result
    }
  } catch (error) {
    console.error("Forgot password request error:", error)
    throw error
  }
}

/**
 * Reset password using a token
 * @param token Reset password token from email link
 * @param password New password
 * @param turnstileToken Cloudflare Turnstile verification token
 * @returns Promise with the response data
 */
export async function resetPassword(token: string, password: string, turnstileToken: string): Promise<any> {
  try {
    // 对密码进行加密，使用与注册和登录相同的加密方式
    const hashedPassword = await encryptPassword(password)

    // 使用API工具类发送请求
    const result = await api.post<{
      code?: number
      data?: any
      msg?: string
      message?: string
    }>("/api/auth/reset-password", {
      token,
      password: hashedPassword, // 发送加密后的密码
      "cf-turnstile-response": turnstileToken,
    })

    // 检查是否有标准API响应格式
    if (result && typeof result === "object" && "code" in result) {
      if (result.code === 200) {
        return result
      } else {
        throw new Error(result.msg || result.message || "Failed to reset password")
      }
    } else {
      // 处理直接返回成功的情况（向后兼容）
      return result
    }
  } catch (error) {
    console.error("Reset password error:", error)
    throw error
  }
}

/**
 * Get user profile information
 * @param userId User ID to fetch
 * @param authToken Authentication token for authorization (deprecated - now handled automatically)
 * @returns Promise with the user data
 */
export async function getUserProfile(userId: string, authToken?: string): Promise<UserProfileData> {
  try {
    // 使用新的API客户端，自动处理认证和错误
    const result = await api.get<
      | {
          code?: number
          data?: UserProfileData
          msg?: string
        }
      | UserProfileData
    >(`/api/users/${userId}`)

    // 检查是否有标准API响应格式
    if (result && typeof result === "object" && "code" in result) {
      if (result.code === 200 && result.data) {
        return result.data
      } else {
        throw new Error(result.msg || "Failed to get user profile")
      }
    } else if (result && typeof result === "object" && "id" in result) {
      // 处理直接返回数据的情况（向后兼容）
      return result as UserProfileData
    } else {
      throw new Error("Invalid response format from server")
    }
  } catch (error) {
    console.error("Get user profile error:", error)
    throw error
  }
}

/**
 * Update user profile information
 * @param userId User ID to update
 * @param data Profile data to update
 * @param authToken Authentication token for authorization (deprecated - now handled automatically)
 * @returns Promise with the response data
 */
export async function updateUserProfile(
  userId: string,
  data: UpdateUserProfileRequest,
  authToken?: string
): Promise<UserProfileData | null> {
  try {
    // 直接使用数据，不需要字段转换，因为前后端都使用 expertise
    const requestData = { ...data }

    // 使用新的API客户端，自动处理认证和错误
    const result = await api.patch<
      | {
          code?: number
          data?: UserProfileData
          msg?: string
        }
      | UserProfileData
      | null
    >(`/api/users/${userId}`, requestData)

    // 检查是否有标准API响应格式
    if (result && typeof result === "object" && "code" in result) {
      if (result.code === 200) {
        return result.data || null
      } else {
        throw new Error(result.msg || "Failed to update user profile")
      }
    } else if (result && typeof result === "object" && "id" in result) {
      // 处理直接返回数据的情况（向后兼容）
      return result as UserProfileData
    } else {
      // 可能返回null表示只是成功消息
      return result as UserProfileData | null
    }
  } catch (error) {
    console.error("Update user profile error:", error)
    throw error
  }
}

/**
 * Update user email address
 * @param userId User ID to update
 * @param email New email address
 * @param authToken Authentication token for authorization (deprecated - now handled automatically)
 * @returns Promise with the response data
 */
export async function updateUserEmail(
  userId: string,
  email: string,
  authToken?: string
): Promise<UserProfileData | null> {
  try {
    const requestData: UpdateEmailRequest = { email }

    // 使用新的API客户端，自动处理认证和错误
    return await api.patch<UserProfileData | null>(`/api/users/${userId}/email`, requestData)
  } catch (error) {
    console.error("Update user email error:", error)
    throw error
  }
}

/**
 * Check if username exists
 * @param username Username to check
 * @returns Promise with the check result
 */
export async function checkUsername(username: string): Promise<UsernameCheckResponse> {
  try {
    // 使用GET请求检查用户名是否存在
    return await api.get<UsernameCheckResponse>(`/api/users/check`, { username })
  } catch (error) {
    console.error("Check username error:", error)
    throw error
  }
}
